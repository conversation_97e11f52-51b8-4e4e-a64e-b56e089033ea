package com.moregames.playtime.carousel

import com.moregames.base.bus.MessageBus
import com.moregames.base.junit.MockExtension
import com.moregames.base.util.mock
import com.moregames.playtime.carousel.domain.TaskDefinition
import com.moregames.playtime.carousel.domain.UserCarouselTask
import com.moregames.playtime.user.challenge.progress.ObjectiveProgressCalculatorType
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions
import java.time.Instant
import java.util.*

@ExtendWith(MockExtension::class)
class CarouselTaskLifecycleHandlersTest(
  private val carouselService: CarouselService,
  private val messageBus: MessageBus,
) {
  private val underTest = CarouselTaskLifecycleHandlers(carouselService, messageBus)

  private companion object {
    const val USER_ID = "user123"
    const val GAME_ID = 1001
    val TASK_ID = UUID.randomUUID()
    val BLOCKED_UNTIL = Instant.parse("2023-12-01T12:00:00Z")

    val enabledTaskDefinition = TaskDefinition(
      id = "task_def_123",
      gameId = GAME_ID,
      titleTranslation = "Complete 5 levels",
      icon = "level_icon",
      progressMax = 100,
      goal = 5,
      order = 1,
      calculator = ObjectiveProgressCalculatorType.MILESTONE,
      enabled = true,
    )

    val disabledTaskDefinition = enabledTaskDefinition.copy(enabled = false)
  }

  @Test
  fun `SHOULD mark task as finished and create new task ON handleCarouselRecreateTaskCommand WHEN task is Claimed and definition is enabled`() = runBlocking {
    val claimedTask = UserCarouselTask.Claimed(TASK_ID, USER_ID, enabledTaskDefinition, BLOCKED_UNTIL)
    val command = CarouselRecreateTaskCommand(TASK_ID)

    carouselService.mock({ getTask(TASK_ID) }, claimedTask)

    underTest.handleCarouselRecreateTaskCommand(command)

    verifyBlocking(carouselService) { getTask(TASK_ID) }
    verifyBlocking(carouselService) { markTaskAsFinished(TASK_ID) }
    verifyBlocking(carouselService) { createTask(USER_ID, enabledTaskDefinition.id) }
  }

  @Test
  fun `SHOULD mark task as finished but not create new task ON handleCarouselRecreateTaskCommand WHEN task is Claimed and definition is disabled`() = runBlocking {
    val claimedTask = UserCarouselTask.Claimed(TASK_ID, USER_ID, disabledTaskDefinition, BLOCKED_UNTIL)
    val command = CarouselRecreateTaskCommand(TASK_ID)

    carouselService.mock({ getTask(TASK_ID) }, claimedTask)

    underTest.handleCarouselRecreateTaskCommand(command)

    verifyBlocking(carouselService) { getTask(TASK_ID) }
    verifyBlocking(carouselService) { markTaskAsFinished(TASK_ID) }
    verifyBlocking(carouselService, times = 0) { createTask(any(), any()) }
  }

  @Test
  fun `SHOULD return early ON handleCarouselRecreateTaskCommand WHEN task is InProgress`() = runBlocking {
    val inProgressTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, enabledTaskDefinition, 50, null)
    val command = CarouselRecreateTaskCommand(TASK_ID)

    carouselService.mock({ getTask(TASK_ID) }, inProgressTask)

    underTest.handleCarouselRecreateTaskCommand(command)

    verifyBlocking(carouselService) { getTask(TASK_ID) }
    verifyBlocking(carouselService, times = 0) { markTaskAsFinished(any()) }
    verifyBlocking(carouselService, times = 0) { createTask(any(), any()) }
  }

  @Test
  fun `SHOULD return early ON handleCarouselRecreateTaskCommand WHEN task is New`() = runBlocking {
    val newTask = UserCarouselTask.New(TASK_ID, USER_ID, enabledTaskDefinition)
    val command = CarouselRecreateTaskCommand(TASK_ID)

    carouselService.mock({ getTask(TASK_ID) }, newTask)

    underTest.handleCarouselRecreateTaskCommand(command)

    verifyBlocking(carouselService) { getTask(TASK_ID) }
    verifyBlocking(carouselService, times = 0) { markTaskAsFinished(any()) }
    verifyBlocking(carouselService, times = 0) { createTask(any(), any()) }
  }

  @Test
  fun `SHOULD return early ON handleCarouselRecreateTaskCommand WHEN task is Unclaimed`() = runBlocking {
    val unclaimedTask = UserCarouselTask.Unclaimed(TASK_ID, USER_ID, enabledTaskDefinition)
    val command = CarouselRecreateTaskCommand(TASK_ID)

    carouselService.mock({ getTask(TASK_ID) }, unclaimedTask)

    underTest.handleCarouselRecreateTaskCommand(command)

    verifyBlocking(carouselService) { getTask(TASK_ID) }
    verifyBlocking(carouselService, times = 0) { markTaskAsFinished(any()) }
    verifyBlocking(carouselService, times = 0) { createTask(any(), any()) }
  }

  @Test
  fun `SHOULD return early ON handleCarouselRecreateTaskCommand WHEN task is Completed`() = runBlocking {
    val completedTask = UserCarouselTask.Completed(TASK_ID, USER_ID, enabledTaskDefinition)
    val command = CarouselRecreateTaskCommand(TASK_ID)

    carouselService.mock({ getTask(TASK_ID) }, completedTask)

    underTest.handleCarouselRecreateTaskCommand(command)

    verifyBlocking(carouselService) { getTask(TASK_ID) }
    verifyBlocking(carouselService, times = 0) { markTaskAsFinished(any()) }
    verifyBlocking(carouselService, times = 0) { createTask(any(), any()) }
  }

  @Test
  fun `SHOULD handle different task ID ON handleCarouselRecreateTaskCommand WHEN task is Claimed and definition is enabled`() = runBlocking {
    val differentTaskId = UUID.randomUUID()
    val differentUserId = "different_user_456"
    val differentTaskDefinition = enabledTaskDefinition.copy(id = "different_task_def_789")
    val claimedTask = UserCarouselTask.Claimed(differentTaskId, differentUserId, differentTaskDefinition, BLOCKED_UNTIL)
    val command = CarouselRecreateTaskCommand(differentTaskId)

    carouselService.mock({ getTask(differentTaskId) }, claimedTask)

    underTest.handleCarouselRecreateTaskCommand(command)

    verifyBlocking(carouselService) { getTask(differentTaskId) }
    verifyBlocking(carouselService) { markTaskAsFinished(differentTaskId) }
    verifyBlocking(carouselService) { createTask(differentUserId, differentTaskDefinition.id) }
  }
}
